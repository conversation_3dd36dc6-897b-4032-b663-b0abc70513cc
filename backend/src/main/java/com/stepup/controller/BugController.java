package com.stepup.controller;

import com.stepup.dto.DataResponseDTO;
import com.stepup.dto.bug.CreateBugDTO;
import com.stepup.model.bug.FeatureBug;
import com.stepup.service.BugService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping("/api/bugs")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class BugController {
    private final BugService bugService;

    @Autowired
    public BugController(BugService bugService) {
        this.bugService = bugService;
    }

    @PostMapping
    public ResponseEntity<DataResponseDTO<?>> createBug(
            @RequestPart("bug") CreateBugDTO createBugDTO,
            @RequestPart(value = "evidence", required = false) MultipartFile[] evidenceFiles
    ) {
        try {
            FeatureBug createdFeatureBug = bugService.createBug(createBugDTO, evidenceFiles);
            return ResponseEntity.ok(DataResponseDTO.success(createdFeatureBug, "Bug created successfully"));
        } catch (Exception e) {
            return ResponseEntity.internalServerError()
                    .body(DataResponseDTO.error("Error: " + e.getMessage(), "BUG_CREATION_FAILED"));
        }
    }
} 