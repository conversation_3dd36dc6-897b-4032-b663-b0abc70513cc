package com.stepup.controller;

import com.stepup.service.FileStorageService;
import com.stepup.service.S3Service;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Path;
import java.nio.file.Paths;

@RestController
@RequestMapping("/api/files")
@CrossOrigin(origins = {"http://localhost:3000", "http://localhost:3001"})
public class FileController {

    private static final Logger log = LoggerFactory.getLogger(FileController.class);
    
    private final S3Service s3Service;
    private final FileStorageService fileStorageService;

    @Autowired
    public FileController(S3Service s3Service, FileStorageService fileStorageService) {
        this.s3Service = s3Service;
        this.fileStorageService = fileStorageService;
    }

    /**
     * Get file URL - for S3 files, returns public URL; for local files, returns download endpoint
     * @param fileName The file name/object key
     * @return File URL or download endpoint
     */
    @GetMapping("/url/{fileName}")
    public ResponseEntity<String> getFileUrl(@PathVariable String fileName) {
        try {
            if (s3Service.isS3Enabled() && fileName.contains("/")) {
                // Assume S3 file if contains folder separator
                String s3Url = s3Service.getFileUrl(fileName);
                if (s3Url != null) {
                    return ResponseEntity.ok(s3Url);
                }
            }
            
            // Return local download URL
            String downloadUrl = "/api/files/download/" + fileName;
            return ResponseEntity.ok(downloadUrl);
            
        } catch (Exception e) {
            log.error("Error getting file URL for: {}", fileName, e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Download file from local storage
     * This is used as fallback when S3 is not configured
     * @param fileName The local file name
     * @return File resource
     */
    @GetMapping("/download/{fileName}")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName) {
        try {
            // Load file from local storage
            Path fileStorageLocation = Paths.get("uploads").toAbsolutePath().normalize();
            Path filePath = fileStorageLocation.resolve(fileName).normalize();
            
            Resource resource = new UrlResource(filePath.toUri());
            
            if (resource.exists() && resource.isReadable()) {
                // Try to determine file's content type
                String contentType = "application/octet-stream"; // Default content type
                
                try {
                    contentType = java.nio.file.Files.probeContentType(filePath);
                    if (contentType == null) {
                        contentType = "application/octet-stream";
                    }
                } catch (IOException ex) {
                    log.info("Could not determine file type for: {}", fileName);
                }

                return ResponseEntity.ok()
                        .contentType(MediaType.parseMediaType(contentType))
                        .header(HttpHeaders.CONTENT_DISPOSITION, "inline; filename=\"" + resource.getFilename() + "\"")
                        .body(resource);
            } else {
                log.error("File not found or not readable: {}", fileName);
                return ResponseEntity.notFound().build();
            }
        } catch (MalformedURLException ex) {
            log.error("File not found: {}", fileName, ex);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * Check if a file exists (works for both S3 and local storage)
     * @param fileName The file name/object key
     * @return Boolean indicating if file exists
     */
    @GetMapping("/exists/{fileName}")
    public ResponseEntity<Boolean> fileExists(@PathVariable String fileName) {
        try {
            boolean exists = false;
            
            if (s3Service.isS3Enabled() && fileName.contains("/")) {
                // Check S3
                exists = s3Service.fileExists(fileName);
            } else {
                // Check local storage
                Path fileStorageLocation = Paths.get("uploads").toAbsolutePath().normalize();
                Path filePath = fileStorageLocation.resolve(fileName).normalize();
                exists = java.nio.file.Files.exists(filePath);
            }
            
            return ResponseEntity.ok(exists);
        } catch (Exception e) {
            log.error("Error checking file existence for: {}", fileName, e);
            return ResponseEntity.ok(false);
        }
    }
} 