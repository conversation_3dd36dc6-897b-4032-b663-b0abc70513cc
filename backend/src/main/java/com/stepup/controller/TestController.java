package com.stepup.controller;

import com.stepup.service.GoogleChatService;
import com.stepup.service.S3Service;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {

    private final GoogleChatService googleChatService;
    private final S3Service s3Service;

    public TestController(GoogleChatService googleChatService, S3Service s3Service) {
        this.googleChatService = googleChatService;
        this.s3Service = s3Service;
    }

    @PostMapping("/google-chat")
    public ResponseEntity<String> testGoogleChat(@RequestBody Map<String, String> payload) {
        String message = payload.get("message");
        if (message == null || message.trim().isEmpty()) {
            return ResponseEntity.badRequest().body("Message cannot be empty");
        }
        googleChatService.sendMessage(message);
        return ResponseEntity.ok("Message sent to Google Chat successfully.");
    }

    /**
     * Test S3 upload với multiple files
     */
    @PostMapping("/s3-upload-multiple")
    public ResponseEntity<Map<String, Object>> testS3UploadMultiple(
            @RequestParam(value = "files", required = false) MultipartFile[] files) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!s3Service.isS3Enabled()) {
                response.put("success", false);
                response.put("message", "S3 service is not enabled or configured");
                response.put("storage_type", "local_fallback");
                return ResponseEntity.badRequest().body(response);
            }

            // Handle case when no files are provided
            if (files == null || files.length == 0) {
                response.put("success", true);
                response.put("message", "No files provided for upload");
                response.put("total_files", 0);
                response.put("success_count", 0);
                response.put("failure_count", 0);
                response.put("upload_results", new HashMap<>());
                response.put("storage_type", "s3");
                response.put("note", "This is a valid test case - no files were uploaded");
                return ResponseEntity.ok(response);
            }

            Map<String, Object> uploadResults = new HashMap<>();
            int successCount = 0;
            int failureCount = 0;

            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                Map<String, Object> fileResult = new HashMap<>();
                
                try {
                    if (file != null && !file.isEmpty()) {
                        String objectKey = s3Service.uploadFile(file);
                        String fileUrl = s3Service.getFileUrl(objectKey);
                        
                        fileResult.put("success", true);
                        fileResult.put("object_key", objectKey);
                        fileResult.put("file_url", fileUrl);
                        fileResult.put("original_filename", file.getOriginalFilename());
                        fileResult.put("file_size", file.getSize());
                        fileResult.put("content_type", file.getContentType());
                        successCount++;
                    } else {
                        fileResult.put("success", false);
                        fileResult.put("message", file == null ? "File is null" : "File is empty");
                        fileResult.put("original_filename", file != null ? file.getOriginalFilename() : "null");
                        failureCount++;
                    }
                } catch (Exception e) {
                    fileResult.put("success", false);
                    fileResult.put("message", "Failed to upload: " + e.getMessage());
                    fileResult.put("error", e.getClass().getSimpleName());
                    fileResult.put("original_filename", file != null ? file.getOriginalFilename() : "unknown");
                    failureCount++;
                }
                
                uploadResults.put("file_" + i, fileResult);
            }

            response.put("success", successCount > 0 || (successCount == 0 && failureCount == 0));
            response.put("message", String.format("Upload completed: %d successful, %d failed out of %d files", 
                successCount, failureCount, files.length));
            response.put("total_files", files.length);
            response.put("success_count", successCount);
            response.put("failure_count", failureCount);
            response.put("upload_results", uploadResults);
            response.put("storage_type", "s3");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to upload files: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Test S3 service status
     */
    @GetMapping("/s3-status")
    public ResponseEntity<Map<String, Object>> testS3Status() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("s3_enabled", s3Service.isS3Enabled());
        
        if (s3Service.isS3Enabled()) {
            response.put("status", "S3 service is enabled and ready");
            response.put("storage_type", "s3");
        } else {
            response.put("status", "S3 service is disabled - using local storage");
            response.put("storage_type", "local");
        }
        
        return ResponseEntity.ok(response);
    }

    /**
     * Test file existence in S3
     */
    @GetMapping("/s3-file-exists")
    public ResponseEntity<Map<String, Object>> testS3FileExists(@RequestParam("objectKey") String objectKey) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!s3Service.isS3Enabled()) {
                response.put("success", false);
                response.put("message", "S3 service is not enabled");
                return ResponseEntity.badRequest().body(response);
            }

            boolean exists = s3Service.fileExists(objectKey);
            String fileUrl = exists ? s3Service.getFileUrl(objectKey) : null;

            response.put("success", true);
            response.put("object_key", objectKey);
            response.put("exists", exists);
            response.put("file_url", fileUrl);

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to check file existence: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Test upload with ETag và get detailed file info
     */
    @PostMapping("/s3-upload-with-etag")
    public ResponseEntity<Map<String, Object>> testS3UploadWithETag(
            @RequestParam(value = "files", required = false) MultipartFile[] files) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!s3Service.isS3Enabled()) {
                response.put("success", false);
                response.put("message", "S3 service is not enabled or configured");
                return ResponseEntity.badRequest().body(response);
            }

            if (files == null || files.length == 0) {
                response.put("success", true);
                response.put("message", "No files provided for upload");
                response.put("files_uploaded", new HashMap<>());
                return ResponseEntity.ok(response);
            }

            Map<String, Object> uploadResults = new HashMap<>();
            
            for (int i = 0; i < files.length; i++) {
                MultipartFile file = files[i];
                Map<String, Object> fileResult = new HashMap<>();
                
                try {
                    if (file != null && !file.isEmpty()) {
                        // Upload với ETag info
                        S3Service.UploadResult result = s3Service.uploadFileWithETag(file, "test");
                        
                        fileResult.put("success", true);
                        fileResult.put("object_key", result.getObjectKey());
                        fileResult.put("etag", result.getEtag());
                        fileResult.put("file_url", result.getFileUrl());
                        fileResult.put("original_filename", result.getOriginalFileName());
                        fileResult.put("file_size", result.getFileSize());
                        fileResult.put("content_type", result.getContentType());
                        
                        // Demonstrate ETag usage
                        fileResult.put("etag_info", Map.of(
                            "description", "ETag is used for data integrity and change detection",
                            "format", result.getEtag().contains("-") ? "multipart" : "single_part",
                            "note", "Save this ETag to detect future changes to the file"
                        ));
                        
                    } else {
                        fileResult.put("success", false);
                        fileResult.put("message", file == null ? "File is null" : "File is empty");
                    }
                } catch (Exception e) {
                    fileResult.put("success", false);
                    fileResult.put("message", "Upload failed: " + e.getMessage());
                    fileResult.put("error", e.getClass().getSimpleName());
                }
                
                uploadResults.put("file_" + i, fileResult);
            }

            response.put("success", true);
            response.put("message", "Upload completed with ETag information");
            response.put("files_uploaded", uploadResults);
            response.put("etag_usage", Map.of(
                "integrity", "Use ETag to verify file hasn't been corrupted",
                "caching", "Compare ETag to see if file has changed",
                "deduplication", "Use ETag to detect duplicate files"
            ));

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to upload files: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Test ETag comparison - check if file has changed
     */
    @GetMapping("/s3-check-etag")
    public ResponseEntity<Map<String, Object>> testETagComparison(
            @RequestParam("objectKey") String objectKey,
            @RequestParam("expectedETag") String expectedETag) {
        
        Map<String, Object> response = new HashMap<>();
        
        try {
            if (!s3Service.isS3Enabled()) {
                response.put("success", false);
                response.put("message", "S3 service is not enabled");
                return ResponseEntity.badRequest().body(response);
            }

            // Get current file metadata
            S3Service.FileMetadata metadata = s3Service.getFileMetadata(objectKey);
            
            if (metadata == null) {
                response.put("success", false);
                response.put("message", "File not found");
                response.put("object_key", objectKey);
                return ResponseEntity.notFound().build();
            }

            boolean unchanged = s3Service.isFileUnchanged(objectKey, expectedETag);
            String currentETag = metadata.getEtag();

            response.put("success", true);
            response.put("object_key", objectKey);
            response.put("expected_etag", expectedETag);
            response.put("current_etag", currentETag);
            response.put("file_unchanged", unchanged);
            response.put("last_modified", metadata.getLastModified().toString());
            response.put("file_size", metadata.getContentLength());
            response.put("content_type", metadata.getContentType());
            response.put("file_url", metadata.getFileUrl());
            
            response.put("interpretation", unchanged ? 
                "File content has NOT changed since last check" : 
                "File content HAS CHANGED - ETag differs from expected");

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "Failed to check ETag: " + e.getMessage());
            response.put("error", e.getClass().getSimpleName());
            return ResponseEntity.internalServerError().body(response);
        }
    }
} 