package com.stepup.dto.bug;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.model.bug.Priority;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BugResDTO {
    @JsonProperty("id")
    private String id;

    @JsonProperty("id_prefix")
    private String idPrefix;

    @JsonProperty("project_id")
    private String projectId;

    @JsonProperty("testcase_id")
    private String testcaseId;

    @JsonProperty("title")
    private String title;

    @JsonProperty("description")
    private String description;

    @JsonProperty("steps_to_reproduce")
    private String stepsToReproduce;

    @JsonProperty("actual_result")
    private String actualResult;

    @JsonProperty("expected_result")
    private String expectedResult;

    @JsonProperty("priority")
    private Priority priority;

    @JsonProperty("evidence_images")
    private String evidenceImages;

    @JsonProperty("created_by")
    private String createdBy;
}