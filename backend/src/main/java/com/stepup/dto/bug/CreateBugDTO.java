package com.stepup.dto.bug;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.stepup.model.bug.Priority;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CreateBugDTO {
    @JsonProperty("id_prefix")
    private String idPrefix;

    @JsonProperty("test_case_id")
    private String testcaseId;

    private String title;

    private String description;

    @JsonProperty("steps_to_reproduce")
    private String stepsToReproduce;

    @JsonProperty("actual_result")
    private String actualResult;

    private Priority priority;
}