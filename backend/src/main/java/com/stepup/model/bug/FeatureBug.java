package com.stepup.model.bug;

import com.stepup.model.ModifierTrackingEntity;
import jakarta.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.annotations.GenericGenerator;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Table(name = "feature_bugs")
public class FeatureBug extends ModifierTrackingEntity {
    @Id
    @GeneratedValue(generator = "UUID")
    @GenericGenerator(name = "UUID", strategy = "org.hibernate.id.UUIDGenerator")
    @Column(name = "id", columnDefinition = "VARCHAR(50)")
    private String id;

    @Column(name = "project_id")
    private String projectId;

    @Column(name = "testcase_id")
    private String testcaseId;

    @Column(name = "title")
    private String title;

    @Lob
    @Column(name = "description")
    private String description;

    @Lob
    @Column(name = "steps_to_reproduce")
    private String stepsToReproduce;

    @Lob
    @Column(name = "actual_result")
    private String actualResult;

    @Lob
    @Column(name = "expected_result")
    private String expectedResult;

    @Enumerated(EnumType.STRING)
    @Column(name = "priority")
    private Priority priority;

    @Column(name = "evidence_images")
    private String evidenceImages;
} 