package com.stepup.repository;

import com.stepup.model.bug.FeatureBug;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface BugRepository extends JpaRepository<FeatureBug, String> {
    /**
     * Find all bugs associated with a specific project
     * @param projectId The ID of the project
     * @return List of bugs for the project
     */
    List<FeatureBug> findByProjectId(String projectId);
} 