package com.stepup.service;

import com.stepup.dto.bug.BugResDTO;
import com.stepup.dto.bug.CreateBugDTO;
import com.stepup.model.bug.FeatureBug;
import com.stepup.model.test.TestCase;
import com.stepup.repository.BugRepository;
import com.stepup.repository.TestCaseRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class BugService {

    private final BugRepository bugRepository;
    private final TestCaseRepository testCaseRepository;
    private final FileStorageService fileStorageService;
    private final S3Service s3Service;

    @Autowired
    public BugService(BugRepository bugRepository, TestCaseRepository testCaseRepository, 
                     FileStorageService fileStorageService, S3Service s3Service) {
        this.bugRepository = bugRepository;
        this.testCaseRepository = testCaseRepository;
        this.fileStorageService = fileStorageService;
        this.s3Service = s3Service;
    }

    public FeatureBug createBug(CreateBugDTO createBugDTO, MultipartFile[] evidenceFiles) {
        TestCase testCase = testCaseRepository.findById(createBugDTO.getTestcaseId())
                .orElseThrow(() -> new RuntimeException("TestCase not found with id: " + createBugDTO.getTestcaseId()));

        String evidenceFileNames = null;
        if (evidenceFiles != null && evidenceFiles.length > 0) {
            List<String> fileNames = new ArrayList<>();
            for (MultipartFile file : evidenceFiles) {
                if (!file.isEmpty()) {
                    String fileName = storeFile(file);
                    fileNames.add(fileName);
                }
            }
            if (!fileNames.isEmpty()) {
                evidenceFileNames = String.join(",", fileNames);
            }
        }

        // This is a simplified way to get expected result. 
        // In a real application, you would parse the 'data' field of TestCase, which is a JSON string.
        String expectedResult = "Expected result from " + testCase.getId();

        FeatureBug featureBug = new FeatureBug();
        featureBug.setIdPrefix(createBugDTO.getIdPrefix());
        featureBug.setTestcaseId(testCase.getId());
        featureBug.setTitle(createBugDTO.getTitle());
        featureBug.setDescription(createBugDTO.getDescription());
        featureBug.setStepsToReproduce(createBugDTO.getStepsToReproduce());
        featureBug.setActualResult(createBugDTO.getActualResult());
        featureBug.setExpectedResult(expectedResult);
        featureBug.setPriority(createBugDTO.getPriority());
        featureBug.setEvidenceImages(evidenceFileNames);

        return bugRepository.save(featureBug);
    }

    /**
     * Store file using S3 if configured, otherwise use local storage
     * @param file MultipartFile to store
     * @return File identifier (S3 object key or local file name)
     */
    private String storeFile(MultipartFile file) {
        if (s3Service.isS3Enabled()) {
            // Use S3 storage
            return s3Service.uploadFile(file, "evidence");
        } else {
            // Fall back to local storage
            return fileStorageService.storeFile(file);
        }
    }

    /**
     * Get all bugs for a specific project
     * @param projectId The ID of the project to get bugs for
     * @return List of bug DTOs associated with the project
     */
    public List<BugResDTO> getBugsByProjectId(String projectId) {
        List<FeatureBug> bugs = bugRepository.findByProjectId(projectId);
        return bugs.stream()
                .map(this::mapToBugResDTO)
                .collect(Collectors.toList());
    }

    /**
     * Map FeatureBug entity to BugResDTO
     */
    private BugResDTO mapToBugResDTO(FeatureBug bug) {
        return BugResDTO.builder()
                .id(bug.getId())
                .idPrefix(bug.getIdPrefix())
                .projectId(bug.getProjectId())
                .testcaseId(bug.getTestcaseId())
                .title(bug.getTitle())
                .description(bug.getDescription())
                .stepsToReproduce(bug.getStepsToReproduce())
                .actualResult(bug.getActualResult())
                .expectedResult(bug.getExpectedResult())
                .priority(bug.getPriority())
                .evidenceImages(bug.getEvidenceImages())
                .build();
    }
} 