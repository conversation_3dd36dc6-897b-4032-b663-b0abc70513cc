package com.stepup.service;

import com.stepup.common.Constant;
import jakarta.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials;
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider;
import software.amazon.awssdk.core.sync.RequestBody;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.s3.S3Client;
import software.amazon.awssdk.services.s3.model.*;

import java.io.IOException;
import java.net.URI;
import java.util.UUID;

@Service
public class S3Service {

    private static final Logger log = LoggerFactory.getLogger(S3Service.class);
    private S3Client s3Client;

    @Value("${cdn_domain:}")
    private String cdnDomain;

    @Value("${s3.aws.hostname:}")
    private String s3Hostname;

    @Value("${s3.aws.access_key:}")
    private String s3AccessKey;

    @Value("${s3.aws.secret:}")
    private String s3Secret;

    @Value("${s3.aws.region:us-east-1}")
    private String s3Region;

    @Value("${s3.aws.bucket_name:}")
    private String bucketName;

    @PostConstruct
    public void initializeS3Client() {
        try {
            // Only initialize if S3 configuration is provided
            if (StringUtils.hasText(s3AccessKey) && StringUtils.hasText(s3Secret) && StringUtils.hasText(bucketName)) {
                Region region = Region.of(s3Region);
                AwsBasicCredentials awsBasicCredentials = AwsBasicCredentials.create(s3AccessKey, s3Secret);

                if (StringUtils.hasText(s3Hostname)) {
                    // For custom S3-compatible endpoints
                    this.s3Client = S3Client.builder()
                            .endpointOverride(URI.create(s3Hostname))
                            .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                            .region(region)
                            .build();
                } else {
                    // For standard AWS S3
                    this.s3Client = S3Client.builder()
                            .credentialsProvider(StaticCredentialsProvider.create(awsBasicCredentials))
                            .region(region)
                            .build();
                }

                log.info("S3Client initialized successfully for region: {}", s3Region);
            } else {
                log.warn("S3 configuration not provided, S3Service will be disabled");
            }
        } catch (Exception e) {
            log.error("Failed to initialize S3Client: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to initialize S3Client", e);
        }
    }

    /**
     * Check if S3 service is enabled and configured
     */
    public boolean isS3Enabled() {
        return s3Client != null;
    }

    /**
     * Upload a MultipartFile to S3 and return the object key
     * @param file MultipartFile to upload
     * @param folder Optional folder prefix (e.g., "evidence", "documents")
     * @return Object key of the uploaded file
     */
    public String uploadFile(MultipartFile file, String folder) {
        UploadResult result = uploadFileWithETag(file, folder);
        return result.getObjectKey();
    }

    /**
     * Upload a MultipartFile to S3 and return upload info including ETag
     * @param file MultipartFile to upload
     * @param folder Optional folder prefix (e.g., "evidence", "documents")
     * @return Upload result with object key and ETag
     */
    public UploadResult uploadFileWithETag(MultipartFile file, String folder) {
        if (!isS3Enabled()) {
            throw new RuntimeException("S3 service is not enabled or configured");
        }
        
        if (file == null || file.isEmpty()) {
            throw new IllegalArgumentException("File cannot be null or empty");
        }
        
        try {
            String originalFileName = StringUtils.cleanPath(file.getOriginalFilename());
            String fileExtension = getFileExtension(originalFileName);
            String objectKey = generateObjectKey(folder, fileExtension);
            
            PutObjectRequest putRequest = PutObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .contentType(file.getContentType())
                    .contentLength(file.getSize())
                    .build();
            
            PutObjectResponse response = s3Client.putObject(putRequest, 
                    RequestBody.fromInputStream(file.getInputStream(), file.getSize()));
            
            String etag = response.eTag();
            String fileUrl = getFileUrl(objectKey);
            
            log.info("Successfully uploaded file to S3. ObjectKey: {}, ETag: {}", objectKey, etag);
            
            return new UploadResult(
                objectKey, 
                etag, 
                fileUrl, 
                originalFileName, 
                file.getSize(), 
                file.getContentType()
            );
            
        } catch (IOException e) {
            log.error("Error reading file input stream: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to read file", e);
        } catch (S3Exception e) {
            log.error("Error uploading file to S3: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to upload file to S3", e);
        }
    }

    /**
     * Upload a MultipartFile to S3 with default "uploads" folder
     * @param file MultipartFile to upload
     * @return Object key of the uploaded file
     */
    public String uploadFile(MultipartFile file) {
        return uploadFile(file, Constant.DEFAULT_S3_BUCKET + "/evidence");
    }

    /**
     * Get the public URL of an S3 object
     * @param objectKey The object key in S3
     * @return Public URL of the object
     */
    public String getFileUrl(String objectKey) {
        if (!isS3Enabled()) {
            return null;
        }
        return cdnDomain + "/" + objectKey;
    }

    /**
     * Delete a file from S3
     * @param objectKey The object key to delete
     * @return true if deletion was successful, false otherwise
     */
    public boolean deleteFile(String objectKey) {
        if (!isS3Enabled()) {
            return false;
        }

        try {
            DeleteObjectRequest deleteRequest = DeleteObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();

            s3Client.deleteObject(deleteRequest);
            log.info("Successfully deleted file from S3. ObjectKey: {}", objectKey);
            return true;

        } catch (S3Exception e) {
            log.error("Error deleting file from S3. ObjectKey: {}, Error: {}", objectKey, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if a file exists in S3
     * @param objectKey The object key to check
     * @return true if file exists, false otherwise
     */
    public boolean fileExists(String objectKey) {
        if (!isS3Enabled()) {
            return false;
        }

        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();

            s3Client.headObject(headRequest);
            return true;

        } catch (NoSuchKeyException e) {
            return false;
        } catch (S3Exception e) {
            log.error("Error checking if file exists. ObjectKey: {}, Error: {}", objectKey, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Check if file content has changed by comparing ETag
     * @param objectKey The object key in S3
     * @param expectedETag The expected ETag to compare with
     * @return true if ETags match (file unchanged), false if different or file not found
     */
    public boolean isFileUnchanged(String objectKey, String expectedETag) {
        if (!isS3Enabled()) {
            return false;
        }
        
        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();
            
            HeadObjectResponse response = s3Client.headObject(headRequest);
            String actualETag = response.eTag();
            
            boolean unchanged = expectedETag.equals(actualETag);
            log.debug("File {} - Expected ETag: {}, Actual ETag: {}, Unchanged: {}", 
                objectKey, expectedETag, actualETag, unchanged);
            
            return unchanged;
            
        } catch (NoSuchKeyException e) {
            log.debug("File {} not found", objectKey);
            return false;
        } catch (S3Exception e) {
            log.error("Error checking file ETag. ObjectKey: {}, Error: {}", objectKey, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get file metadata including ETag
     * @param objectKey The object key in S3
     * @return File metadata or null if not found
     */
    public FileMetadata getFileMetadata(String objectKey) {
        if (!isS3Enabled()) {
            return null;
        }
        
        try {
            HeadObjectRequest headRequest = HeadObjectRequest.builder()
                    .bucket(bucketName)
                    .key(objectKey)
                    .build();
            
            HeadObjectResponse response = s3Client.headObject(headRequest);
            
            return new FileMetadata(
                objectKey,
                response.eTag(),
                response.contentLength(),
                response.contentType(),
                response.lastModified(),
                getFileUrl(objectKey)
            );
            
        } catch (NoSuchKeyException e) {
            log.debug("File {} not found", objectKey);
            return null;
        } catch (S3Exception e) {
            log.error("Error getting file metadata. ObjectKey: {}, Error: {}", objectKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * Generate a unique object key with optional folder prefix
     */
    private String generateObjectKey(String folder, String fileExtension) {
        String fileName = UUID.randomUUID().toString() + fileExtension;

        if (StringUtils.hasText(folder)) {
            return folder + "/" + fileName;
        }

        return fileName;
    }

    /**
     * Extract file extension from filename
     */
    private String getFileExtension(String fileName) {
        if (!StringUtils.hasText(fileName)) {
            return "";
        }

        int lastDotIndex = fileName.lastIndexOf(".");
        if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) {
            return fileName.substring(lastDotIndex);
        }

        return "";
    }

    // Inner classes for return types
    public static class UploadResult {
        private final String objectKey;
        private final String etag;
        private final String fileUrl;
        private final String originalFileName;
        private final long fileSize;
        private final String contentType;
        
        public UploadResult(String objectKey, String etag, String fileUrl, 
                           String originalFileName, long fileSize, String contentType) {
            this.objectKey = objectKey;
            this.etag = etag;
            this.fileUrl = fileUrl;
            this.originalFileName = originalFileName;
            this.fileSize = fileSize;
            this.contentType = contentType;
        }
        
        // Getters
        public String getObjectKey() { return objectKey; }
        public String getEtag() { return etag; }
        public String getFileUrl() { return fileUrl; }
        public String getOriginalFileName() { return originalFileName; }
        public long getFileSize() { return fileSize; }
        public String getContentType() { return contentType; }
    }
    
    public static class FileMetadata {
        private final String objectKey;
        private final String etag;
        private final long contentLength;
        private final String contentType;
        private final java.time.Instant lastModified;
        private final String fileUrl;
        
        public FileMetadata(String objectKey, String etag, long contentLength, 
                           String contentType, java.time.Instant lastModified, String fileUrl) {
            this.objectKey = objectKey;
            this.etag = etag;
            this.contentLength = contentLength;
            this.contentType = contentType;
            this.lastModified = lastModified;
            this.fileUrl = fileUrl;
        }
        
        // Getters
        public String getObjectKey() { return objectKey; }
        public String getEtag() { return etag; }
        public long getContentLength() { return contentLength; }
        public String getContentType() { return contentType; }
        public java.time.Instant getLastModified() { return lastModified; }
        public String getFileUrl() { return fileUrl; }
    }
} 