# Example S3 Configuration
# Copy these properties to your application.properties or application.yml and fill in your actual values

# S3 Configuration (optional - if not provided, local storage will be used)
s3.aws.hostname=https://s3.amazonaws.com
# For AWS S3, leave hostname empty or use default
# For S3-compatible services (like MinIO), provide custom endpoint
# s3.aws.hostname=http://localhost:9000

s3.aws.access_key=your-access-key
s3.aws.secret=your-secret-key
s3.aws.region=us-east-1
s3.aws.bucket_name=your-bucket-name

# Examples for different S3 providers:

# AWS S3 (default)
# s3.aws.hostname=
# s3.aws.access_key=AKIAIOSFODNN7EXAMPLE
# s3.aws.secret=wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY
# s3.aws.region=us-east-1
# s3.aws.bucket_name=my-app-bucket

# MinIO (self-hosted S3-compatible)
# s3.aws.hostname=http://localhost:9000
# s3.aws.access_key=minioadmin
# s3.aws.secret=minioadmin
# s3.aws.region=us-east-1
# s3.aws.bucket_name=my-app-bucket

# DigitalOcean Spaces
# s3.aws.hostname=https://nyc3.digitaloceanspaces.com
# s3.aws.access_key=your-spaces-key
# s3.aws.secret=your-spaces-secret
# s3.aws.region=us-east-1
# s3.aws.bucket_name=your-space-name 