spring.application.name=demo

# Security Configuration
spring.security.user.name=admin
spring.security.user.password=admin
spring.autoconfigure.exclude=org.springframework.boot.autoconfigure.security.servlet.UserDetailsServiceAutoConfiguration

# Database Configuration
spring.datasource.url=******************************************
spring.datasource.driver-class-name=org.postgresql.Driver
spring.datasource.username=stepup
spring.datasource.password=stepup
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true

# JWT Configuration
# Base64 encoded secret (at least 32 bytes when decoded for HS256)
app.jwt.secret=${JWT_SECRET:2f9c5a2c12b3cb2fed2a74ce3310bdf8adb86c8a50df44ddf76f8c2d0df6316b}
app.jwt.expiration-ms=86400000

# OpenAI Configuration
openai.api.key=hi
openai.api.model=gpt-4o-mini
openai.api.timeout=60
openai.api.max-tokens=2000
openai.api.temperature=0.7
openai.api.url=https://api.openai.com/v1/chat/completions

spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB

# Swagger OpenAPI Configuration
swagger.openapi.local-url=http://localhost:8080
swagger.openapi.dev-url=
swagger.openapi.staging-url=
swagger.openapi.prod-url=

# Logging Configuration for AOP
logging.level.com.stepup.aspect.LastAccessUpdateAspect=DEBUG

# Sepay Webhook Configuration
sepay.webhook.api-key=${SEPAY_WEBHOOK_API_KEY:your_sepay_api_key_here}
sepay.webhook.ip-whitelist=${SEPAY_WEBHOOK_IP:*************}
sepay.webhook.enabled=${SEPAY_WEBHOOK_ENABLED:true}

# Sepay Account Configuration
sepay.account.number=${SEPAY_ACCOUNT_NUMBER:your_account_number}
sepay.account.name=${SEPAY_ACCOUNT_NAME:Your Account Name}
sepay.bank.code=${SEPAY_BANK_CODE:VCB}

# Sepay Payment Configuration
sepay.payment.prefix=${SEPAY_PAYMENT_PREFIX:TA}
sepay.payment.currency=${SEPAY_PAYMENT_CURRENCY:VND}
sepay.payment.timeout=${SEPAY_PAYMENT_TIMEOUT:300000}

# Logging Configuration for Sepay
logging.level.com.stepup.service.SepayWebhookService=INFO
logging.level.com.stepup.controller.SepayWebhookController=INFO

# Google Chat Webhook URL
google.chat.webhook.url=https://chat.googleapis.com/v1/spaces/AAQAIs4YtYg/messages?key=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI&token=x7Q6B8j2Rxysf3R3UNFzlExfc5MiwF-ZGojVhpfHaik

# S3 Configuration (optional - if not provided, local storage will be used)
cdn_domain=https://smedia.stepup.edu.vn
s3.aws.hostname=https://storage.googleapis.com
s3.aws.access_key=GOOGCZHD6SQSXRS2BP3XXH7Z
s3.aws.secret=RRno4bBPRFzUEe6WZx0WrwV8M5KReVdwlK5XO9FP
s3.aws.bucket_name=sumedia
s3.aws.region=ASIA-SOUTHEAST1